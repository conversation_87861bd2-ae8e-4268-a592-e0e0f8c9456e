<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ProjectController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Project::with('client');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('titolo', 'like', "%{$search}%")
                  ->orWhere('tipologia_lavoro', 'like', "%{$search}%")
                  ->orWhere('descrizione', 'like', "%{$search}%")
                  ->orWhereHas('client', function ($clientQuery) use ($search) {
                      $clientQuery->where('nome', 'like', "%{$search}%")
                                  ->orWhere('cognome', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by client
        if ($request->filled('client_id')) {
            $query->where('client_id', $request->client_id);
        }

        // Filter by project type
        if ($request->filled('tipo_progetto')) {
            $query->where('tipo_progetto', $request->tipo_progetto);
        }

        // Filter by status
        if ($request->filled('stato')) {
            $query->where('stato', $request->stato);
        }

        $projects = $query->latest()->paginate(10)->withQueryString();
        $clients = Client::orderBy('nome')->get();

        return view('projects.index', compact('projects', 'clients'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $clients = Client::orderBy('nome')->get();
        $selectedClient = $request->client_id ? Client::find($request->client_id) : null;

        return view('projects.create', compact('clients', 'selectedClient'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
            'titolo' => 'required|string|max:255',
            'tipologia_lavoro' => 'required|string|max:1000',
            'tipo_progetto' => 'required|in:One-shot,Ricorrente',
            'data_scadenza' => 'nullable|date|after:today',
            'frequenza' => 'nullable|string|max:255',
            'compenso' => 'required|numeric|min:0',
            'stato' => 'required|in:In Attesa,In Corso,Completato,Annullato',
            'descrizione' => 'nullable|string|max:2000',
            'note' => 'nullable|string|max:1000',
        ]);

        // Validation rules based on project type
        if ($validated['tipo_progetto'] === 'One-shot') {
            $request->validate([
                'data_scadenza' => 'required|date|after:today',
            ]);
        } elseif ($validated['tipo_progetto'] === 'Ricorrente') {
            $request->validate([
                'frequenza' => 'required|string|max:255',
            ]);
        }

        Project::create($validated);

        return redirect()->route('projects.index')
            ->with('success', 'Progetto creato con successo.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Project $project)
    {
        $project->load('client');
        return view('projects.show', compact('project'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Project $project)
    {
        $clients = Client::orderBy('nome')->get();
        return view('projects.edit', compact('project', 'clients'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Project $project)
    {
        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
            'titolo' => 'required|string|max:255',
            'tipologia_lavoro' => 'required|string|max:1000',
            'tipo_progetto' => 'required|in:One-shot,Ricorrente',
            'data_scadenza' => 'nullable|date',
            'frequenza' => 'nullable|string|max:255',
            'compenso' => 'required|numeric|min:0',
            'stato' => 'required|in:In Attesa,In Corso,Completato,Annullato',
            'descrizione' => 'nullable|string|max:2000',
            'note' => 'nullable|string|max:1000',
        ]);

        // Validation rules based on project type
        if ($validated['tipo_progetto'] === 'One-shot') {
            $request->validate([
                'data_scadenza' => 'required|date',
            ]);
        } elseif ($validated['tipo_progetto'] === 'Ricorrente') {
            $request->validate([
                'frequenza' => 'required|string|max:255',
            ]);
        }

        $project->update($validated);

        return redirect()->route('projects.index')
            ->with('success', 'Progetto aggiornato con successo.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Project $project)
    {
        $project->delete();

        return redirect()->route('projects.index')
            ->with('success', 'Progetto eliminato con successo.');
    }
}
