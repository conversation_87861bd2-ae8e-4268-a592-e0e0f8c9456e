<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">Dashboard</h1>
            <p class="text-muted">Benvenuto nel sistema di gestione 36k Agency</p>
        </div>
    </div>

    <?php if(auth()->user()->isAdmin()): ?>
    <!-- Statistics Cards for Admin -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo e(\App\Models\Employee::count()); ?></h4>
                            <p class="card-text">Dipendenti</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo e(\App\Models\Client::count()); ?></h4>
                            <p class="card-text">Clienti</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-handshake fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo e(\App\Models\Project::where('stato', 'In Corso')->count()); ?></h4>
                            <p class="card-text">Progetti Attivi</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-project-diagram fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo e(\App\Models\Project::count()); ?></h4>
                            <p class="card-text">Progetti Totali</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-bar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Projects -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Progetti Recenti</h5>
                </div>
                <div class="card-body">
                    <?php
                        $recentProjects = \App\Models\Project::with('client')->latest()->take(5)->get();
                    ?>
                    <?php if($recentProjects->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Titolo</th>
                                        <th>Cliente</th>
                                        <th>Stato</th>
                                        <th>Data</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentProjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($project->titolo); ?></td>
                                        <td><?php echo e($project->client->full_name); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo e(strtolower(str_replace(' ', '-', $project->stato))); ?>">
                                                <?php echo e($project->stato); ?>

                                            </span>
                                        </td>
                                        <td><?php echo e($project->created_at->format('d/m/Y')); ?></td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">Nessun progetto trovato.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Azioni Rapide</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('employees.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Nuovo Dipendente
                        </a>
                        <a href="<?php echo e(route('clients.create')); ?>" class="btn btn-success">
                            <i class="fas fa-handshake"></i> Nuovo Cliente
                        </a>
                        <a href="<?php echo e(route('projects.create')); ?>" class="btn btn-warning">
                            <i class="fas fa-plus"></i> Nuovo Progetto
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Employee Dashboard -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Benvenuto, <?php echo e(auth()->user()->name); ?></h5>
                </div>
                <div class="card-body">
                    <p>Sei connesso come <strong><?php echo e(auth()->user()->role); ?></strong>.</p>
                    <p>Utilizza il menu di navigazione per accedere alle funzionalità disponibili.</p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Lavori\36k\gestionale\laravel\resources\views/dashboard.blade.php ENDPATH**/ ?>